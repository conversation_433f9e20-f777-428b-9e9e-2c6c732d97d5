use std::time::Duration;

use serde::{Deserialize, Serialize};
use validator::Validate;

use crate::common::client::config::{CircuitBreakerConfig, RetryConfig, ShredstreamClientConfig};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Validate)]
pub struct EndpointConfig {
    #[validate(length(min = 1))]
    pub name: String,
    #[validate(url)]
    pub url: String,
    #[serde(default)]
    pub enabled: bool,
    #[serde(default)]
    pub cooldown_duration: Duration,
    #[serde(flatten)]
    #[validate(nested)]
    pub client_config: SerializableClientConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Validate)]
pub struct SerializableClientConfig {
    pub connect_timeout: Option<Duration>,
    pub subscribe_timeout: Option<Duration>,
    #[validate(nested)]
    pub retry_config: Option<RetryConfig>,
    #[validate(nested)]
    pub circuit_breaker_config: Option<CircuitBreakerConfig>,
}

impl Default for SerializableClientConfig {
    fn default() -> Self {
        Self { connect_timeout: Some(Duration::from_secs(10)), subscribe_timeout: Some(Duration::from_secs(5)), retry_config: Some(RetryConfig::default()), circuit_breaker_config: Some(CircuitBreakerConfig::default()) }
    }
}

impl Default for EndpointConfig {
    fn default() -> Self {
        Self { name: String::new(), url: String::new(), enabled: true, cooldown_duration: Duration::from_secs(300), client_config: SerializableClientConfig::default() }
    }
}

impl EndpointConfig {
    pub fn to_client_config(&self) -> ShredstreamClientConfig {
        ShredstreamClientConfig {
            connect_timeout: self.client_config.connect_timeout,
            subscribe_timeout: self.client_config.subscribe_timeout,
            retry_config: self.client_config.retry_config.clone(),
            circuit_breaker_config: self.client_config.circuit_breaker_config.clone(),
            failure_callback: None,
            disconnect_callback: None,
            retry_attempt_callback: None,
            retry_success_callback: None,
        }
    }
}
