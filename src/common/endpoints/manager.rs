use std::fmt;

use tokio::sync::mpsc;
use tokio_stream::StreamExt;
use tracing::{error, info, warn};

use crate::{
    common::client::{config::ShredstreamSubscriptionFilters, shredstream_client::ShredstreamClient},
    config::endpoints::EndpointConfig,
    generated::Entry,
};

#[derive(Debug, Clone)]
pub enum EndpointManagerError {
    NoEnabledEndpoints,
    ConfigurationError(String),
}

impl fmt::Display for EndpointManagerError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EndpointManagerError::NoEnabledEndpoints => write!(f, "No enabled endpoints found"),
            EndpointManagerError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
        }
    }
}

impl std::error::Error for EndpointManagerError {}

pub struct EndpointManager {
    endpoints: Vec<EndpointConfig>,
    shared_filters: Option<ShredstreamSubscriptionFilters>,
}

impl EndpointManager {
    pub fn new(endpoints: Vec<EndpointConfig>, filters: Option<ShredstreamSubscriptionFilters>) -> Self {
        Self { endpoints, shared_filters: filters }
    }

    pub async fn subscribe(&mut self) -> Result<mpsc::UnboundedReceiver<Entry>, EndpointManagerError> {
        let enabled_endpoints: Vec<_> = self.endpoints.iter().filter(|e| e.enabled).collect();

        if enabled_endpoints.is_empty() {
            return Err(EndpointManagerError::NoEnabledEndpoints);
        }

        let (sender, receiver) = mpsc::unbounded_channel();

        for endpoint_config in enabled_endpoints {
            let sender_clone = sender.clone();
            let endpoint_config_clone = endpoint_config.clone();
            let filters_clone = self.shared_filters.clone();

            tokio::spawn(async move {
                Self::handle_endpoint(endpoint_config_clone, filters_clone, sender_clone).await;
            });
        }

        Ok(receiver)
    }

    async fn handle_endpoint(endpoint_config: EndpointConfig, filters: Option<ShredstreamSubscriptionFilters>, sender: mpsc::UnboundedSender<Entry>) {
        loop {
            info!("Connecting to endpoint: {}", endpoint_config.name);

            let mut client = ShredstreamClient::new(endpoint_config.url.clone(), filters.clone(), Some(endpoint_config.to_client_config()));

            match client.subscribe().await {
                Ok(mut stream) => {
                    info!("Successfully subscribed to endpoint: {}", endpoint_config.name);

                    while let Some(entry_result) = stream.next().await {
                        match entry_result {
                            Ok(entry) => {
                                if sender.send(entry).is_err() {
                                    warn!("Receiver dropped, stopping endpoint: {}", endpoint_config.name);
                                    return;
                                }
                            }
                            Err(e) => {
                                error!("Stream error from {}: {:?}", endpoint_config.name, e);
                                break;
                            }
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to subscribe to {}: {:?}", endpoint_config.name, e);
                }
            }

            info!("Endpoint {} entering cooldown for {:?}", endpoint_config.name, endpoint_config.cooldown_duration);
            tokio::time::sleep(endpoint_config.cooldown_duration).await;
        }
    }
}
