use std::time::Duration;

use serde::{Deserialize, Serialize};
use validator::Validate;

use crate::common::client::config::ShredstreamClientConfig;

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct EndpointConfig {
    #[validate(length(min = 1))]
    pub name: String,
    #[validate(url)]
    pub url: String,
    #[serde(default)]
    pub enabled: bool,
    #[serde(default)]
    pub cooldown_duration: Duration,
    #[serde(flatten)]
    #[validate(nested)]
    pub client_config: ShredstreamClientConfig,
}

impl Default for EndpointConfig {
    fn default() -> Self {
        Self { name: String::new(), url: String::new(), enabled: true, cooldown_duration: Duration::from_secs(300), client_config: ShredstreamClientConfig::default() }
    }
}

impl EndpointConfig {
    pub fn to_client_config(&self) -> ShredstreamClientConfig {
        ShredstreamClientConfig {
            connect_timeout: self.client_config.connect_timeout,
            subscribe_timeout: self.client_config.subscribe_timeout,
            retry_config: self.client_config.retry_config.clone(),
            circuit_breaker_config: self.client_config.circuit_breaker_config.clone(),
            failure_callback: None,
            disconnect_callback: None,
            retry_attempt_callback: None,
            retry_success_callback: None,
        }
    }
}
