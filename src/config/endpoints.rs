use std::time::Duration;

use serde::{Deserialize, Serialize};
use validator::Validate;

use crate::common::client::config::{CircuitBreakerConfig, RetryConfig, ShredstreamClientConfig};

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct EndpointConfig {
    #[validate(length(min = 1))]
    pub name: String,
    #[validate(url)]
    pub url: String,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    #[serde(default = "default_cooldown_duration")]
    pub cooldown_duration: Duration,
    pub connect_timeout: Option<Duration>,
    pub subscribe_timeout: Option<Duration>,
    #[validate(nested)]
    pub retry_config: Option<RetryConfig>,
    #[validate(nested)]
    pub circuit_breaker_config: Option<CircuitBreakerConfig>,
}

fn default_enabled() -> bool {
    true
}

fn default_cooldown_duration() -> Duration {
    Duration::from_secs(300)
}

impl Default for EndpointConfig {
    fn default() -> Self {
        Self {
            name: String::new(),
            url: String::new(),
            enabled: default_enabled(),
            cooldown_duration: default_cooldown_duration(),
            connect_timeout: Some(Duration::from_secs(10)),
            subscribe_timeout: Some(Duration::from_secs(5)),
            retry_config: Some(RetryConfig::default()),
            circuit_breaker_config: Some(CircuitBreakerConfig::default()),
        }
    }
}

impl EndpointConfig {
    pub fn to_client_config(&self) -> ShredstreamClientConfig {
        ShredstreamClientConfig {
            connect_timeout: self.connect_timeout,
            subscribe_timeout: self.subscribe_timeout,
            retry_config: self.retry_config.clone(),
            circuit_breaker_config: self.circuit_breaker_config.clone(),
            failure_callback: None,
            disconnect_callback: None,
            retry_attempt_callback: None,
            retry_success_callback: None,
        }
    }
}
