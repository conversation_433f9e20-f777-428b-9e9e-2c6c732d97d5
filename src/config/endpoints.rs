use std::time::Duration;

use serde::{Deserialize, Serialize};
use validator::Validate;

use crate::common::client::config::ShredstreamClientConfig;

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct EndpointConfig {
    #[validate(length(min = 1))]
    pub name: String,
    #[validate(url)]
    pub url: String,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    #[serde(default = "default_cooldown_duration")]
    pub cooldown_duration: Duration,
    #[serde(flatten)]
    #[validate(nested)]
    pub client_config: ShredstreamClientConfig,
}

fn default_enabled() -> bool {
    true
}

fn default_cooldown_duration() -> Duration {
    Duration::from_secs(300) // 5 minutes
}

impl Default for EndpointConfig {
    fn default() -> Self {
        Self {
            name: String::new(),
            url: String::new(),
            enabled: default_enabled(),
            cooldown_duration: default_cooldown_duration(),
            client_config: ShredstreamClientConfig::default(),
        }
    }
}