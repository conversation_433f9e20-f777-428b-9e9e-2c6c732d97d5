{"$message_type":"diagnostic","message":"cannot find attribute `serde` in this scope","code":null,"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":1751,"byte_end":1756,"line_start":52,"line_end":52,"column_start":7,"column_end":12,"is_primary":true,"text":[{"text":"    #[serde(flatten)]","highlight_start":7,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`serde` is in scope, but it is a crate, not an attribute","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: cannot find attribute `serde` in this scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:52:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[serde(flatten)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `serde` is in scope, but it is a crate, not an attribute\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `subscribe_timeout` on type `ShredstreamClientConfig`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/shredstream_client.rs","byte_start":5987,"byte_end":6004,"line_start":159,"line_end":159,"column_start":66,"column_end":83,"is_primary":true,"text":[{"text":"        let result = if let Some(timeout_duration) = self.config.subscribe_timeout {","highlight_start":66,"highlight_end":83}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a field of the same name","code":null,"level":"help","spans":[{"file_name":"src/common/client/shredstream_client.rs","byte_start":5987,"byte_end":5987,"line_start":159,"line_end":159,"column_start":66,"column_end":66,"is_primary":true,"text":[{"text":"        let result = if let Some(timeout_duration) = self.config.subscribe_timeout {","highlight_start":66,"highlight_end":66}],"label":null,"suggested_replacement":"base.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `subscribe_timeout` on type `ShredstreamClientConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/shredstream_client.rs:159:66\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let result = if let Some(timeout_duration) = self.config.subscribe_timeout {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: one of the expressions' fields has a field of the same name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        let result = if let Some(timeout_duration) = self.config.\u001b[0m\u001b[0m\u001b[38;5;10mbase.\u001b[0m\u001b[0msubscribe_timeout {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `connect_timeout` on type `ShredstreamClientConfig`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/shredstream_client.rs","byte_start":6869,"byte_end":6884,"line_start":185,"line_end":185,"column_start":44,"column_end":59,"is_primary":true,"text":[{"text":"        if let Some(timeout) = self.config.connect_timeout {","highlight_start":44,"highlight_end":59}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a field of the same name","code":null,"level":"help","spans":[{"file_name":"src/common/client/shredstream_client.rs","byte_start":6869,"byte_end":6869,"line_start":185,"line_end":185,"column_start":44,"column_end":44,"is_primary":true,"text":[{"text":"        if let Some(timeout) = self.config.connect_timeout {","highlight_start":44,"highlight_end":44}],"label":null,"suggested_replacement":"base.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `connect_timeout` on type `ShredstreamClientConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/shredstream_client.rs:185:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(timeout) = self.config.connect_timeout {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: one of the expressions' fields has a field of the same name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        if let Some(timeout) = self.config.\u001b[0m\u001b[0m\u001b[38;5;10mbase.\u001b[0m\u001b[0mconnect_timeout {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `connect_timeout` on type `&ShredstreamClientConfig`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":2281,"byte_end":2296,"line_start":64,"line_end":64,"column_start":45,"column_end":60,"is_primary":true,"text":[{"text":"            .field(\"connect_timeout\", &self.connect_timeout)","highlight_start":45,"highlight_end":60}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a field of the same name","code":null,"level":"help","spans":[{"file_name":"src/common/client/config.rs","byte_start":2281,"byte_end":2281,"line_start":64,"line_end":64,"column_start":45,"column_end":45,"is_primary":true,"text":[{"text":"            .field(\"connect_timeout\", &self.connect_timeout)","highlight_start":45,"highlight_end":45}],"label":null,"suggested_replacement":"base.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `connect_timeout` on type `&ShredstreamClientConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:64:45\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .field(\"connect_timeout\", &self.connect_timeout)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: one of the expressions' fields has a field of the same name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .field(\"connect_timeout\", &self.\u001b[0m\u001b[0m\u001b[38;5;10mbase.\u001b[0m\u001b[0mconnect_timeout)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `subscribe_timeout` on type `&ShredstreamClientConfig`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":2344,"byte_end":2361,"line_start":65,"line_end":65,"column_start":47,"column_end":64,"is_primary":true,"text":[{"text":"            .field(\"subscribe_timeout\", &self.subscribe_timeout)","highlight_start":47,"highlight_end":64}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a field of the same name","code":null,"level":"help","spans":[{"file_name":"src/common/client/config.rs","byte_start":2344,"byte_end":2344,"line_start":65,"line_end":65,"column_start":47,"column_end":47,"is_primary":true,"text":[{"text":"            .field(\"subscribe_timeout\", &self.subscribe_timeout)","highlight_start":47,"highlight_end":47}],"label":null,"suggested_replacement":"base.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `subscribe_timeout` on type `&ShredstreamClientConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:65:47\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .field(\"subscribe_timeout\", &self.subscribe_timeout)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: one of the expressions' fields has a field of the same name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .field(\"subscribe_timeout\", &self.\u001b[0m\u001b[0m\u001b[38;5;10mbase.\u001b[0m\u001b[0msubscribe_timeout)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `retry_config` on type `&ShredstreamClientConfig`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":2404,"byte_end":2416,"line_start":66,"line_end":66,"column_start":42,"column_end":54,"is_primary":true,"text":[{"text":"            .field(\"retry_config\", &self.retry_config)","highlight_start":42,"highlight_end":54}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a field of the same name","code":null,"level":"help","spans":[{"file_name":"src/common/client/config.rs","byte_start":2404,"byte_end":2404,"line_start":66,"line_end":66,"column_start":42,"column_end":42,"is_primary":true,"text":[{"text":"            .field(\"retry_config\", &self.retry_config)","highlight_start":42,"highlight_end":42}],"label":null,"suggested_replacement":"base.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `retry_config` on type `&ShredstreamClientConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:66:42\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .field(\"retry_config\", &self.retry_config)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: one of the expressions' fields has a field of the same name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .field(\"retry_config\", &self.\u001b[0m\u001b[0m\u001b[38;5;10mbase.\u001b[0m\u001b[0mretry_config)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `circuit_breaker_config` on type `&ShredstreamClientConfig`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":2469,"byte_end":2491,"line_start":67,"line_end":67,"column_start":52,"column_end":74,"is_primary":true,"text":[{"text":"            .field(\"circuit_breaker_config\", &self.circuit_breaker_config)","highlight_start":52,"highlight_end":74}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a field of the same name","code":null,"level":"help","spans":[{"file_name":"src/common/client/config.rs","byte_start":2469,"byte_end":2469,"line_start":67,"line_end":67,"column_start":52,"column_end":52,"is_primary":true,"text":[{"text":"            .field(\"circuit_breaker_config\", &self.circuit_breaker_config)","highlight_start":52,"highlight_end":52}],"label":null,"suggested_replacement":"base.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `circuit_breaker_config` on type `&ShredstreamClientConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:67:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .field(\"circuit_breaker_config\", &self.circuit_breaker_config)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: one of the expressions' fields has a field of the same name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .field(\"circuit_breaker_config\", &self.\u001b[0m\u001b[0m\u001b[38;5;10mbase.\u001b[0m\u001b[0mcircuit_breaker_config)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ShredstreamClientConfig` has no field named `connect_timeout`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":3043,"byte_end":3058,"line_start":79,"line_end":79,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"            connect_timeout: Some(Duration::from_secs(10)),","highlight_start":13,"highlight_end":28}],"label":"`ShredstreamClientConfig` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `base`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `ShredstreamClientConfig` has no field named `connect_timeout`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:79:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            connect_timeout: Some(Duration::from_secs(10)),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`ShredstreamClientConfig` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ShredstreamClientConfig` has no field named `subscribe_timeout`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":3103,"byte_end":3120,"line_start":80,"line_end":80,"column_start":13,"column_end":30,"is_primary":true,"text":[{"text":"            subscribe_timeout: Some(Duration::from_secs(5)),","highlight_start":13,"highlight_end":30}],"label":"`ShredstreamClientConfig` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `base`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `ShredstreamClientConfig` has no field named `subscribe_timeout`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:80:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            subscribe_timeout: Some(Duration::from_secs(5)),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`ShredstreamClientConfig` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ShredstreamClientConfig` has no field named `retry_config`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":3164,"byte_end":3176,"line_start":81,"line_end":81,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"            retry_config: Some(RetryConfig::default()),","highlight_start":13,"highlight_end":25}],"label":"`ShredstreamClientConfig` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `base`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `ShredstreamClientConfig` has no field named `retry_config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:81:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            retry_config: Some(RetryConfig::default()),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`ShredstreamClientConfig` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ShredstreamClientConfig` has no field named `circuit_breaker_config`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/config.rs","byte_start":3220,"byte_end":3242,"line_start":82,"line_end":82,"column_start":13,"column_end":35,"is_primary":true,"text":[{"text":"            circuit_breaker_config: Some(CircuitBreakerConfig::default()),","highlight_start":13,"highlight_end":35}],"label":"`ShredstreamClientConfig` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `base`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `ShredstreamClientConfig` has no field named `circuit_breaker_config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:82:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            circuit_breaker_config: Some(CircuitBreakerConfig::default()),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`ShredstreamClientConfig` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `retry_config` on type `ShredstreamClientConfig`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/shredstream_client.rs","byte_start":993,"byte_end":1005,"line_start":28,"line_end":28,"column_start":36,"column_end":48,"is_primary":true,"text":[{"text":"        let retry_manager = config.retry_config.as_ref().map(|retry_config| RetryManager::new(retry_config.clone()));","highlight_start":36,"highlight_end":48}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a field of the same name","code":null,"level":"help","spans":[{"file_name":"src/common/client/shredstream_client.rs","byte_start":993,"byte_end":993,"line_start":28,"line_end":28,"column_start":36,"column_end":36,"is_primary":true,"text":[{"text":"        let retry_manager = config.retry_config.as_ref().map(|retry_config| RetryManager::new(retry_config.clone()));","highlight_start":36,"highlight_end":36}],"label":null,"suggested_replacement":"base.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `retry_config` on type `ShredstreamClientConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/shredstream_client.rs:28:36\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let retry_manager = config.retry_config.as_ref().map(|retry_config| RetryManager::new(retry_config.clone()));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: one of the expressions' fields has a field of the same name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        let retry_manager = config.\u001b[0m\u001b[0m\u001b[38;5;10mbase.\u001b[0m\u001b[0mretry_config.as_ref().map(|retry_config| RetryManager::new(retry_config.clone()));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `circuit_breaker_config` on type `ShredstreamClientConfig`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/common/client/shredstream_client.rs","byte_start":1113,"byte_end":1135,"line_start":29,"line_end":29,"column_start":38,"column_end":60,"is_primary":true,"text":[{"text":"        let circuit_breaker = config.circuit_breaker_config.as_ref().map(|cb_config| CircuitBreaker::new(cb_config.clone()));","highlight_start":38,"highlight_end":60}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a field of the same name","code":null,"level":"help","spans":[{"file_name":"src/common/client/shredstream_client.rs","byte_start":1113,"byte_end":1113,"line_start":29,"line_end":29,"column_start":38,"column_end":38,"is_primary":true,"text":[{"text":"        let circuit_breaker = config.circuit_breaker_config.as_ref().map(|cb_config| CircuitBreaker::new(cb_config.clone()));","highlight_start":38,"highlight_end":38}],"label":null,"suggested_replacement":"base.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `circuit_breaker_config` on type `ShredstreamClientConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/shredstream_client.rs:29:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let circuit_breaker = config.circuit_breaker_config.as_ref().map(|cb_config| CircuitBreaker::new(cb_config.clone()));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: one of the expressions' fields has a field of the same name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        let circuit_breaker = config.\u001b[0m\u001b[0m\u001b[38;5;10mbase.\u001b[0m\u001b[0mcircuit_breaker_config.as_ref().map(|cb_config| CircuitBreaker::new(cb_config.clone()));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ShredstreamClientConfig` has no field named `connect_timeout`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/config/endpoints.rs","byte_start":1583,"byte_end":1598,"line_start":48,"line_end":48,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"            connect_timeout: self.client_config.connect_timeout,","highlight_start":13,"highlight_end":28}],"label":"`ShredstreamClientConfig` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `base`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `ShredstreamClientConfig` has no field named `connect_timeout`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/endpoints.rs:48:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            connect_timeout: self.client_config.connect_timeout,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`ShredstreamClientConfig` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ShredstreamClientConfig` has no field named `subscribe_timeout`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/config/endpoints.rs","byte_start":1648,"byte_end":1665,"line_start":49,"line_end":49,"column_start":13,"column_end":30,"is_primary":true,"text":[{"text":"            subscribe_timeout: self.client_config.subscribe_timeout,","highlight_start":13,"highlight_end":30}],"label":"`ShredstreamClientConfig` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `base`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `ShredstreamClientConfig` has no field named `subscribe_timeout`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/endpoints.rs:49:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            subscribe_timeout: self.client_config.subscribe_timeout,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`ShredstreamClientConfig` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ShredstreamClientConfig` has no field named `retry_config`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/config/endpoints.rs","byte_start":1717,"byte_end":1729,"line_start":50,"line_end":50,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"            retry_config: self.client_config.retry_config.clone(),","highlight_start":13,"highlight_end":25}],"label":"`ShredstreamClientConfig` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `base`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `ShredstreamClientConfig` has no field named `retry_config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/endpoints.rs:50:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            retry_config: self.client_config.retry_config.clone(),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`ShredstreamClientConfig` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ShredstreamClientConfig` has no field named `circuit_breaker_config`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/config/endpoints.rs","byte_start":1784,"byte_end":1806,"line_start":51,"line_end":51,"column_start":13,"column_end":35,"is_primary":true,"text":[{"text":"            circuit_breaker_config: self.client_config.circuit_breaker_config.clone(),","highlight_start":13,"highlight_end":35}],"label":"`ShredstreamClientConfig` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `base`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `ShredstreamClientConfig` has no field named `circuit_breaker_config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/endpoints.rs:51:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            circuit_breaker_config: self.client_config.circuit_breaker_config.clone(),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`ShredstreamClientConfig` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `base`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 17 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 17 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0560, E0609.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0560, E0609.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0560`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0560`.\u001b[0m\n"}
