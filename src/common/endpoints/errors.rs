use std::fmt;

#[derive(Debug, Clone)]
pub enum EndpointManagerError {
    NoEnabledEndpoints,
    ConfigurationError(String),
}

impl fmt::Display for EndpointManagerError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EndpointManagerError::NoEnabledEndpoints => write!(f, "No enabled endpoints found"),
            EndpointManagerError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
        }
    }
}

impl std::error::Error for EndpointManagerError {}
