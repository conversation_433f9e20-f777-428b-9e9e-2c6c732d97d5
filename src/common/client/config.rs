use std::time::Duration;

use serde::{Deserialize, Serialize};
use validator::Validate;

pub type FailureCallback = Box<dyn Fn(&str, &crate::common::client::errors::ShredstreamError) + Send + Sync>;
pub type DisconnectCallback = Box<dyn Fn(&str, &crate::common::client::errors::ShredstreamError, Duration, u32) + Send + Sync>;
pub type RetryAttemptCallback = Box<dyn Fn(&str, u32, u32) + Send + Sync>;
pub type RetrySuccessCallback = Box<dyn Fn(&str, u32, Duration) + Send + Sync>;

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct RetryConfig {
    pub enabled: bool,
    pub auto_reset: bool,
    pub max_attempts: u32,
    pub initial_delay: Duration,
    pub max_delay: Duration,
    pub multiplier: f64,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self { enabled: true, auto_reset: true, max_attempts: 3, initial_delay: Duration::from_millis(500), max_delay: Duration::from_secs(30), multiplier: 2.0 }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CircuitBreakerConfig {
    pub failure_threshold: u32,
    pub time_window: Duration,
    pub recovery_timeout: Duration,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self { failure_threshold: 5, time_window: Duration::from_secs(60), recovery_timeout: Duration::from_secs(300) }
    }
}

#[derive(Serialize, Deserialize, Validate)]
pub struct ShredstreamClientConfig {
    pub connect_timeout: Option<Duration>,
    pub subscribe_timeout: Option<Duration>,
    #[validate(nested)]
    pub retry_config: Option<RetryConfig>,
    #[validate(nested)]
    pub circuit_breaker_config: Option<CircuitBreakerConfig>,
    #[serde(skip)]
    pub failure_callback: Option<FailureCallback>,
    #[serde(skip)]
    pub disconnect_callback: Option<DisconnectCallback>,
    #[serde(skip)]
    pub retry_attempt_callback: Option<RetryAttemptCallback>,
    #[serde(skip)]
    pub retry_success_callback: Option<RetrySuccessCallback>,
}

impl Clone for ShredstreamClientConfig {
    fn clone(&self) -> Self {
        Self {
            connect_timeout: self.connect_timeout,
            subscribe_timeout: self.subscribe_timeout,
            retry_config: self.retry_config.clone(),
            circuit_breaker_config: self.circuit_breaker_config.clone(),
            failure_callback: None,
            disconnect_callback: None,
            retry_attempt_callback: None,
            retry_success_callback: None,
        }
    }
}

impl std::fmt::Debug for ShredstreamClientConfig {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ShredstreamClientConfig")
            .field("connect_timeout", &self.connect_timeout)
            .field("subscribe_timeout", &self.subscribe_timeout)
            .field("retry_config", &self.retry_config)
            .field("circuit_breaker_config", &self.circuit_breaker_config)
            .field("failure_callback", &self.failure_callback.as_ref().map(|_| "Some(callback)"))
            .field("disconnect_callback", &self.disconnect_callback.as_ref().map(|_| "Some(callback)"))
            .field("retry_attempt_callback", &self.retry_attempt_callback.as_ref().map(|_| "Some(callback)"))
            .field("retry_success_callback", &self.retry_success_callback.as_ref().map(|_| "Some(callback)"))
            .finish()
    }
}

impl Default for ShredstreamClientConfig {
    fn default() -> Self {
        Self {
            connect_timeout: Some(Duration::from_secs(10)),
            subscribe_timeout: Some(Duration::from_secs(5)),
            retry_config: Some(RetryConfig::default()),
            circuit_breaker_config: Some(CircuitBreakerConfig::default()),
            failure_callback: None,
            disconnect_callback: None,
            retry_attempt_callback: None,
            retry_success_callback: None,
        }
    }
}

#[derive(Debug, Clone, Validate, Default)]
pub struct ShredstreamSubscriptionFilters {
    pub accounts: Option<Vec<String>>,
}
